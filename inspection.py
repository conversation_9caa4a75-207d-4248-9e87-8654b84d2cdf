#!/usr/bin/env python3
import subprocess
import datetime
import sys
import os
import concurrent.futures
import threading
from typing import List, Tu<PERSON>, Dict
import json
import argparse

# 配置类
class Config:
    """配置管理类"""
    def __init__(self):
        self.KGZS_IPS = [
            "************", "************", "************", "************", "************",
            "************", "************", "************", "************", "************",
            "************", "************", "************", "************", "************",
            "************", "************", "************", "************", "************",
            "************", "************", "************", "************", "************",
            "************", "*************", "*************", "*************", "*************",
            "*************"
        ]

        self.CREDENTIALS = {
            "kgzs": {"password": "Kgzs@2024"}
        }
        
        self.SSH_OPTIONS = "-o StrictHostKeyChecking=no -o ConnectTimeout=10 -o ServerAliveInterval=60"
        self.MAX_WORKERS = 10  # 并发连接数

class SSHManager:
    """SSH连接管理类"""
    def __init__(self, config: Config):
        self.config = config
        self.lock = threading.Lock()
        
    def run_command(self, ip: str, user: str, command: str, use_sudo: bool = False) -> Tuple[str, str, bool]:
        """
        执行SSH命令
        返回: (stdout, stderr, success)
        """
        try:
            password = self.config.CREDENTIALS[user]["password"]
            
            if use_sudo:
                ssh_cmd = f"sshpass -p '{password}' ssh {self.config.SSH_OPTIONS} {user}@{ip} \"echo '{password}' | sudo -S {command}\""
            else:
                ssh_cmd = f"sshpass -p '{password}' ssh {self.config.SSH_OPTIONS} {user}@{ip} \"{command}\""
            
            result = subprocess.run(ssh_cmd, shell=True, capture_output=True, text=True, timeout=30)
            return result.stdout, result.stderr, result.returncode == 0
            
        except subprocess.TimeoutExpired:
            return "", f"连接超时 (IP: {ip})", False
        except Exception as e:
            return "", f"错误: {str(e)}", False

class Logger:
    """日志管理类"""
    def __init__(self, log_dir: str = "logs"):
        self.log_dir = log_dir
        os.makedirs(log_dir, exist_ok=True)
        
        timestamp = datetime.datetime.now().strftime("%Y%m%d%H%M")
        self.log_filename = os.path.join(log_dir, f"{timestamp}.log")
        self.json_filename = os.path.join(log_dir, f"{timestamp}.json")
        self.lock = threading.Lock()
        self.results = []
        
    def write(self, content: str, print_console: bool = True, use_color: bool = False):
        """
        写入日志
        content: 日志内容
        print_console: 是否在控制台打印
        use_color: 是否在控制台使用颜色（仅影响控制台输出，不影响文件输出）
        """
        with self.lock:
            # 移除内容中的ANSI颜色码后写入文件
            clean_content = self._remove_ansi_codes(content)
            with open(self.log_filename, 'a', encoding='utf-8') as f:
                f.write(clean_content)
            
            if print_console:
                if use_color:
                    # 如果需要颜色且终端支持，直接打印原内容
                    print(content, end='')
                else:
                    # 否则打印清理后的内容
                    print(clean_content, end='')
    
    def write_colored(self, content: str, color_code: str = "", print_console: bool = True):
        """
        写入带颜色的日志（仅在控制台显示颜色）
        content: 日志内容
        color_code: ANSI颜色码（如 '\033[31m' 表示红色）
        """
        if color_code and sys.stdout.isatty():  # 只在终端环境下使用颜色
            colored_content = f"{color_code}{content}\033[0m"
        else:
            colored_content = content
            
        # 文件中写入无颜色版本，控制台显示带颜色版本
        with self.lock:
            with open(self.log_filename, 'a', encoding='utf-8') as f:
                f.write(content)
            if print_console:
                print(colored_content, end='')
    
    def _remove_ansi_codes(self, text: str) -> str:
        """移除文本中的ANSI颜色码"""
        import re
        ansi_escape = re.compile(r'\x1B(?:[@-Z\\-_]|\[[0-?]*[ -/]*[@-~])')
        return ansi_escape.sub('', text)
                
    def add_result(self, result: Dict):
        """添加结果到JSON"""
        with self.lock:
            self.results.append(result)
            
    def save_json(self):
        """保存JSON结果"""
        with open(self.json_filename, 'w', encoding='utf-8') as f:
            json.dump(self.results, f, ensure_ascii=False, indent=2)

class LustreChecker:
    """Lustre检查主类"""
    def __init__(self, config: Config, logger: Logger):
        self.config = config
        self.logger = logger
        self.ssh_manager = SSHManager(config)
        
    def check_host(self, ip: str, user: str, checks: List[str]) -> Dict:
        """检查单个主机"""
        host_result = {
            "ip": ip,
            "user": user,
            "timestamp": datetime.datetime.now().isoformat(),
            "checks": {}
        }
        
        for check in checks:
            if check == "list_nids":
                stdout, stderr, success = self.ssh_manager.run_command(
                    ip, user, "lctl list_nids", use_sudo=(user == "kgzs")
                )
                host_result["checks"]["list_nids"] = {
                    "success": success,
                    "output": stdout.strip() if success else stderr
                }
                
            elif check == "lustre_mount":
                stdout, stderr, success = self.ssh_manager.run_command(
                    ip, user, "df -h | grep -E '(lustre|o2ib)'", use_sudo=False
                )
                host_result["checks"]["lustre_mount"] = {
                    "success": success,
                    "output": stdout.strip() if success else stderr
                }
                
            elif check == "config_file":
                stdout, stderr, success = self.ssh_manager.run_command(
                    ip, user, "grep ExecStart /etc/systemd/system/exa-client-deploy.service 2>/dev/null || echo 'File not found'", 
                    use_sudo=False
                )
                host_result["checks"]["config_file"] = {
                    "success": success,
                    "output": stdout.strip() if stdout else stderr
                }
                
        return host_result
    
    def check_hosts_parallel(self, hosts: List[Tuple[str, str]], checks: List[str]):
        """并行检查多个主机"""
        with concurrent.futures.ThreadPoolExecutor(max_workers=self.config.MAX_WORKERS) as executor:
            future_to_host = {
                executor.submit(self.check_host, ip, user, checks): (ip, user) 
                for ip, user in hosts
            }
            
            for future in concurrent.futures.as_completed(future_to_host):
                ip, user = future_to_host[future]
                try:
                    result = future.result()
                    self.logger.add_result(result)
                    self.format_and_log_result(result)
                except Exception as e:
                    self.logger.write(f"检查 {ip} ({user}) 时出错: {str(e)}\n")
                    
    def format_and_log_result(self, result: Dict):
        """格式化并记录结果"""
        ip = result["ip"]
        user = result["user"]
        
        # 使用新的带颜色日志方法
        self.logger.write_colored(f"\nIP: {ip} (用户: {user})\n", "\033[31m")
        
        for check_name, check_result in result["checks"].items():
            if check_result["success"]:
                self.logger.write_colored(f"{check_name}: ✓\n", "\033[32m")  # 绿色表示成功
                if check_result["output"]:
                    self.logger.write(f"{check_result['output']}\n")
            else:
                self.logger.write_colored(f"{check_name}: ✗ - {check_result['output']}\n", "\033[31m")  # 红色表示失败
                
        self.logger.write("-" * 40 + "\n")
        
    def run_all_checks(self):
        """运行所有检查"""
        start_time = datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        self.logger.write_colored(f"检查开始时间: {start_time}\n", "\033[36m")  # 青色
        self.logger.write(f"日志文件: {self.logger.log_filename}\n")
        self.logger.write(f"JSON结果: {self.logger.json_filename}\n\n")
        
        # 准备主机列表
        kgzs_hosts = [(ip, "kgzs") for ip in self.config.KGZS_IPS]
        
        # 检查1: lctl list_nids
        self.logger.write("\n" + "="*80 + "\n")
        self.logger.write_colored("1. 检查 lctl list_nids 输出\n", "\033[33m")  # 黄色标题
        self.logger.write("="*80 + "\n")

        self.logger.write_colored("\n### KGZS用户设备 ###\n", "\033[35m")  # 紫色
        self.check_hosts_parallel(kgzs_hosts, ["list_nids"])
        
        # 检查2: Lustre挂载
        self.logger.write("\n" + "="*80 + "\n")
        self.logger.write_colored("2. 检查 /lustre 挂载目录\n", "\033[33m")  # 黄色标题
        self.logger.write("="*80 + "\n")

        self.logger.write_colored("\n### KGZS用户设备 ###\n", "\033[35m")  # 紫色
        self.check_hosts_parallel(kgzs_hosts, ["lustre_mount"])
        
        # 检查3: 配置文件
        self.logger.write("\n" + "="*80 + "\n")
        self.logger.write_colored("3. 检查配置文件\n", "\033[33m")  # 黄色标题
        self.logger.write("="*80 + "\n")

        self.logger.write_colored("\n### KGZS用户设备 ###\n", "\033[35m")  # 紫色
        self.check_hosts_parallel(kgzs_hosts, ["config_file"])
        
        # 保存结果
        self.logger.save_json()
        end_time = datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        self.logger.write_colored(f"\n检查结束时间: {end_time}\n", "\033[36m")  # 青色
        self.logger.write_colored(f"日志已保存到: {self.logger.log_filename}\n", "\033[32m")  # 绿色
        self.logger.write_colored(f"JSON结果已保存到: {self.logger.json_filename}\n", "\033[32m")  # 绿色

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="Lustre文件系统检查工具")
    parser.add_argument("--log-dir", default="logs", help="日志目录")
    parser.add_argument("--max-workers", type=int, default=10, help="最大并发数")
    parser.add_argument("--config", help="配置文件路径（JSON格式）")
    parser.add_argument("--no-color", action="store_true", help="禁用彩色输出")
    
    args = parser.parse_args()
    
    # 初始化配置
    config = Config()
    if args.max_workers:
        config.MAX_WORKERS = args.max_workers
        
    # 如果提供了配置文件，从文件加载配置
    if args.config and os.path.exists(args.config):
        with open(args.config, 'r') as f:
            custom_config = json.load(f)
            if "kgzs_ips" in custom_config:
                config.KGZS_IPS = custom_config["kgzs_ips"]
            if "credentials" in custom_config:
                config.CREDENTIALS.update(custom_config["credentials"])
    
    # 初始化日志
    logger = Logger(args.log_dir)
    
    # 如果指定了禁用颜色，修改日志记录行为
    if args.no_color:
        # 可以在这里添加禁用颜色的逻辑
        pass
    
    # 运行检查
    checker = LustreChecker(config, logger)
    checker.run_all_checks()

if __name__ == "__main__":
    main()